param(
  [Parameter(Mandatory=$true)]
  [string]$Path
)

$ErrorActionPreference = 'SilentlyContinue'

function Out-Json($obj) { $obj | ConvertTo-Json -Depth 6 }

$result = [ordered]@{
  path        = $Path
  exists      = $false
  type        = "missing"
  attributes  = $null
  isReparse   = $false
  linkType    = $null
  target      = $null
  accessible  = $false
  fileCount   = 0
  dirCount    = 0
  bytes       = 0
  errors      = @()
}

try {
  $item = Get-Item -LiteralPath $Path -Force -ErrorAction Stop
  $result.exists = $true
  $result.attributes = $item.Attributes.ToString()
  $result.isReparse = ($item.Attributes -band [IO.FileAttributes]::ReparsePoint) -ne 0
  $result.type = if ($item.PSIsContainer) { "directory" } else { "file" }

  if ($item.PSObject.Properties.Match('LinkType').Count -gt 0) { $result.linkType = $item.LinkType }
  if ($item.PSObject.Properties.Match('Target').Count  -gt 0) { $result.target   = $item.Target }

  if ($result.type -eq "directory") {
    try {
      $dirs  = Get-ChildItem -LiteralPath $Path -Recurse -Force -Directory -ErrorAction Stop
      $files = Get-ChildItem -LiteralPath $Path -Recurse -Force -File      -ErrorAction Stop
      $result.accessible = $true
      $result.dirCount = $dirs.Count
      $result.fileCount = $files.Count
      $result.bytes = ($files | Measure-Object -Property Length -Sum).Sum
    } catch {
      $result.errors += $_.Exception.Message
      $result.accessible = $false
    }
  } else {
    $result.accessible = $true
    $result.fileCount = 1
    $result.bytes = $item.Length
  }

  if ($result.isReparse -and -not $result.target) {
    $fsutil = & cmd /c "fsutil reparsepoint query ""$Path""" 2>$null
    if ($LASTEXITCODE -eq 0) {
      $result.linkType = "reparsepoint"
      $result.target = ($fsutil -join "`n")
    }
  }
}
catch {
  $result.errors += $_.Exception.Message
}

Out-Json $result