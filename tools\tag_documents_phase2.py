import json
import os

INPUT_FILE = r"C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\documents_filelist.jsonl"
OUTPUT_FILE = r"C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\documents_filelist_tagged.jsonl"

TAGS = {
    ".py": "code_candidate",
    ".js": "code_candidate",
    ".ts": "code_candidate",
    ".html": "ui_component",
    ".css": "ui_component",
    ".scss": "ui_component",
    ".jsx": "ui_component",
    ".tsx": "ui_component",
    ".sql": "config_file",
    ".env": "config_file",
    ".json": "config_file",
    ".yaml": "config_file",
    ".yml": "config_file",
    ".ini": "config_file",
    ".ps1": "backend_logic",
    ".sh": "backend_logic"
}

def auto_tag(entry):
    ext = os.path.splitext(entry["filename"])[1].lower()
    return TAGS.get(ext, "unclear_purpose")

def main():
    if not os.path.exists(INPUT_FILE):
        print(f"[ERROR] Input file not found: {INPUT_FILE}")
        return

    with open(INPUT_FILE, "r", encoding="utf-8") as infile, \
         open(OUTPUT_FILE, "w", encoding="utf-8") as outfile:

        for line in infile:
            try:
                entry = json.loads(line)
                entry["notes"] = auto_tag(entry)
                outfile.write(json.dumps(entry) + "\n")
            except Exception as e:
                print(f"[WARN] Failed to process line: {e}")

    print(f"[✓] Tagged file saved to: {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
