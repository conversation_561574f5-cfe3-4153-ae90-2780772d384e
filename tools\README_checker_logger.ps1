param(
    [string]$ReportPath = "C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\top_level_verification.jsonl",
    [string]$LogPath = "C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\verification_log.csv"
)

function Read-MultilineJsonL {
    param([string]$File)
    $items = @()
    $sb = New-Object System.Text.StringBuilder
    $depth = 0
    Get-Content -LiteralPath $File | ForEach-Object {
        $line = $_
        $depth += ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
        $depth -= ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
        [void]$sb.AppendLine($line)
        if ($depth -eq 0 -and $sb.Length -gt 0) {
            $json = $sb.ToString()
            if ($json.Trim()) { $items += ($json | ConvertFrom-Json) }
            $sb.Clear() | Out-Null
        }
    }
    return $items
}

if (-Not (Test-Path -LiteralPath $ReportPath)) {
    Write-Error "JSONL file not found: $ReportPath"
    exit 1
}

$items = Read-MultilineJsonL -File $ReportPath
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

$csvExists = Test-Path $LogPath
if (-Not $csvExists) {
    "Timestamp,Path,Exists,Accessible,Type,FileCount,Bytes,Error" | Out-File -FilePath $LogPath -Encoding utf8
}

foreach ($item in $items) {
    $error = ""
    if (-not $item.exists -or -not $item.accessible) {
        $error = "Missing or inaccessible"
    } elseif ($item.bytes -lt 1 -and $item.fileCount -gt 0) {
        $error = "Zero bytes despite files"
    }
    "$timestamp,""{0}"",{1},{2},{3},{4},{5},{6}" -f `
        $item.path.Replace('"','""'),
        $item.exists,
        $item.accessible,
        $item.type,
        $item.fileCount,
        $item.bytes,
        $error `
    | Add-Content -Path $LogPath
}

Write-Host "`n✅ Audit log appended: $LogPath"
