{
    "path":  "C:\\Users\\<USER>\\Documents\\.pytest_cache",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  4,
    "dirCount":  2,
    "bytes":  542,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\.venv",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory, Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  10207,
    "dirCount":  1018,
    "bytes":  189778897,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\.vs",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  50,
    "dirCount":  8,
    "bytes":  1459235014,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\.vscode",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1306,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\000_another_try_at_excellence",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  54645,
    "dirCount":  6562,
    "bytes":  653631116,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\000-NEW PROJECT-GROK CODE",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  16,
    "dirCount":  0,
    "bytes":  8396527,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\000-RECOVERED COMPLIANCEMAX FILES",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  105558,
    "dirCount":  584,
    "bytes":  19338355629,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\20250802 - Repo Notes",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  23,
    "dirCount":  1,
    "bytes":  22302927,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ALL CHAT DISCUSSIONS",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  42,
    "dirCount":  2,
    "bytes":  4326610,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ALL NEW APP",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  228007,
    "dirCount":  23217,
    "bytes":  5962798722,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ALL NEW APP DESCRIPTIVES",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  2,
    "dirCount":  0,
    "bytes":  43628,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ALL-NEW-APP-CLEANED",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  26,
    "dirCount":  9,
    "bytes":  37784,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\augment-projects",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  0,
    "dirCount":  1,
    "bytes":  null,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\CBCD Application V2",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  11376,
    "dirCount":  397,
    "bytes":  223281850,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\CBCS",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  406651,
    "dirCount":  29879,
    "bytes":  89810200933,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\cbcs web site",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  2,
    "dirCount":  0,
    "bytes":  372271742,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\codex_review",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  6827,
    "dirCount":  710,
    "bytes":  2503284969,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\COMPLAINCEMAX_CLEAN",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  187000,
    "dirCount":  22625,
    "bytes":  13669643714,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\compliancemax",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  37248,
    "dirCount":  5407,
    "bytes":  431993542,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\compliancemax_clean",
    "exists":  true,
    "type":  "directory",
    "attributes":  "ReadOnly, Directory, Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  8448,
    "dirCount":  1054,
    "bytes":  144045466,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax_Cursor_Recovery_Starter",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory, Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  84339,
    "dirCount":  4564,
    "bytes":  4324967178,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax_Cursor_Recovery_Starter2",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  84395,
    "dirCount":  6150,
    "bytes":  2332150748,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\compliancemax_final",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  25,
    "dirCount":  0,
    "bytes":  313715,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax_Rebuild_2025",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  70,
    "dirCount":  51,
    "bytes":  799675,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\compliancemax-fresh",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  23264,
    "dirCount":  2110,
    "bytes":  478720008,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\compliancemax-frontend",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  19145,
    "dirCount":  1922,
    "bytes":  405738435,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax20250403",
    "exists":  true,
    "type":  "directory",
    "attributes":  "ReadOnly, Directory, Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  91198,
    "dirCount":  5570,
    "bytes":  2036007342,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceScan",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  2409,
    "dirCount":  226,
    "bytes":  37540990,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\CursorChatRecovery",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  24,
    "dirCount":  0,
    "bytes":  371625830,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\EaseUS_Recovery",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  5932,
    "dirCount":  2037,
    "bytes":  15429619513,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_binary_salvage",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  null,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_final_recovered",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  null,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_large_files",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  3,
    "dirCount":  2,
    "bytes":  1086043821,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_large_files_extracted",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  7761,
    "dirCount":  0,
    "bytes":  0,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_recovery",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  21,
    "dirCount":  9,
    "bytes":  1086070114,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_recovery_extracted",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  null,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_retry",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  21,
    "dirCount":  9,
    "bytes":  1086070114,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\git_stash_retry_extracted",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  null,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\GitHub workflow results",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  5,
    "dirCount":  1,
    "bytes":  1832134,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\grok-cli",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  47,
    "dirCount":  19,
    "bytes":  57239,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\IISExpress",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  3,
    "dirCount":  3,
    "bytes":  84956,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\maxpmp",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  10603,
    "dirCount":  2021,
    "bytes":  630968770,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\MAXPMP-ACCESS",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  45,
    "dirCount":  0,
    "bytes":  567927,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\monorepo-compliancemax-analysis",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1264,
    "dirCount":  1009,
    "bytes":  3087938593,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\monorepo-compliancemax-analysis-broken",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  9072,
    "dirCount":  270,
    "bytes":  8700730514,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\monorepo-compliancemax-analysis-cleaned",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  69208,
    "dirCount":  1194,
    "bytes":  10859416777,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\monorepo-compliancemax-analysis.bfg",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  30,
    "dirCount":  41,
    "bytes":  972137,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\MORE ZIP-05-20-25",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory, Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  252,
    "dirCount":  48,
    "bytes":  19230237214,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\My Music",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Hidden, System, Directory, ReparsePoint, NotContentIndexed",
    "isReparse":  true,
    "linkType":  "reparsepoint",
    "target":  "Reparse Tag Value : 0xa0000003\nTag value: Microsoft\nTag value: Name Surrogate\nTag value: Mount Point\nSubstitue Name offset: 0\nSubstitue Name length: 44\nPrint Name offset:     46\nPrint Name Length:     36\nSubstitute Name:       \\??\\C:\\Users\\<USER>\\Music\nPrint Name:            C:\\Users\\<USER>\\Music\n\nReparse Data Length: 0x5c      \nReparse Data:\n0000:  00 00 2c 00 2e 00 24 00  5c 00 3f 00 3f 00 5c 00  ..,...$.\\.?.?.\\.\n0010:  43 00 3a 00 5c 00 55 00  73 00 65 00 72 00 73 00  C.:.\\.U.s.e.r.s.\n0020:  5c 00 4d 00 61 00 78 00  5c 00 4d 00 75 00 73 00  \\.M.a.x.\\.M.u.s.\n0030:  69 00 63 00 00 00 43 00  3a 00 5c 00 55 00 73 00  i.c...C.:.\\.U.s.\n0040:  65 00 72 00 73 00 5c 00  4d 00 61 00 78 00 5c 00  e.r.s.\\.M.a.x.\\.\n0050:  4d 00 75 00 73 00 69 00  63 00 00 00              M.u.s.i.c...",
    "accessible":  false,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  0,
    "errors":  [
                   "Access to the path \u0027C:\\Users\\<USER>\\Documents\\My Music\u0027 is denied."
               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\My Pictures",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Hidden, System, Directory, ReparsePoint, NotContentIndexed",
    "isReparse":  true,
    "linkType":  "reparsepoint",
    "target":  "Reparse Tag Value : 0xa0000003\nTag value: Microsoft\nTag value: Name Surrogate\nTag value: Mount Point\nSubstitue Name offset: 0\nSubstitue Name length: 50\nPrint Name offset:     52\nPrint Name Length:     42\nSubstitute Name:       \\??\\C:\\Users\\<USER>\\Pictures\nPrint Name:            C:\\Users\\<USER>\\Pictures\n\nReparse Data Length: 0x68      \nReparse Data:\n0000:  00 00 32 00 34 00 2a 00  5c 00 3f 00 3f 00 5c 00  ..2.4.*.\\.?.?.\\.\n0010:  43 00 3a 00 5c 00 55 00  73 00 65 00 72 00 73 00  C.:.\\.U.s.e.r.s.\n0020:  5c 00 4d 00 61 00 78 00  5c 00 50 00 69 00 63 00  \\.M.a.x.\\.P.i.c.\n0030:  74 00 75 00 72 00 65 00  73 00 00 00 43 00 3a 00  t.u.r.e.s...C.:.\n0040:  5c 00 55 00 73 00 65 00  72 00 73 00 5c 00 4d 00  \\.U.s.e.r.s.\\.M.\n0050:  61 00 78 00 5c 00 50 00  69 00 63 00 74 00 75 00  a.x.\\.P.i.c.t.u.\n0060:  72 00 65 00 73 00 00 00                           r.e.s...",
    "accessible":  false,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  0,
    "errors":  [
                   "Access to the path \u0027C:\\Users\\<USER>\\Documents\\My Pictures\u0027 is denied."
               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\My Videos",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Hidden, System, Directory, ReparsePoint, NotContentIndexed",
    "isReparse":  true,
    "linkType":  "reparsepoint",
    "target":  "Reparse Tag Value : 0xa0000003\nTag value: Microsoft\nTag value: Name Surrogate\nTag value: Mount Point\nSubstitue Name offset: 0\nSubstitue Name length: 46\nPrint Name offset:     48\nPrint Name Length:     38\nSubstitute Name:       \\??\\C:\\Users\\<USER>\\Videos\nPrint Name:            C:\\Users\\<USER>\\Videos\n\nReparse Data Length: 0x60      \nReparse Data:\n0000:  00 00 2e 00 30 00 26 00  5c 00 3f 00 3f 00 5c 00  ....0.\u0026.\\.?.?.\\.\n0010:  43 00 3a 00 5c 00 55 00  73 00 65 00 72 00 73 00  C.:.\\.U.s.e.r.s.\n0020:  5c 00 4d 00 61 00 78 00  5c 00 56 00 69 00 64 00  \\.M.a.x.\\.V.i.d.\n0030:  65 00 6f 00 73 00 00 00  43 00 3a 00 5c 00 55 00  e.o.s...C.:.\\.U.\n0040:  73 00 65 00 72 00 73 00  5c 00 4d 00 61 00 78 00  s.e.r.s.\\.M.a.x.\n0050:  5c 00 56 00 69 00 64 00  65 00 6f 00 73 00 00 00  \\.V.i.d.e.o.s...",
    "accessible":  false,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  0,
    "errors":  [
                   "Access to the path \u0027C:\\Users\\<USER>\\Documents\\My Videos\u0027 is denied."
               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\My Web Sites",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  5,
    "dirCount":  1,
    "bytes":  402251,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\PA-CHECK",
    "exists":  true,
    "type":  "directory",
    "attributes":  "1048624",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  256259,
    "dirCount":  23626,
    "bytes":  11350013164,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\pa-check-mirror",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  25,
    "dirCount":  9,
    "bytes":  1256083,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\PA-CHECK-MM",
    "exists":  true,
    "type":  "directory",
    "attributes":  "System, Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  48438,
    "dirCount":  5670,
    "bytes":  176371986,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\PA-CHECK-MM-clean",
    "exists":  true,
    "type":  "directory",
    "attributes":  "System, Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  162,
    "dirCount":  48,
    "bytes":  1228230,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\PA-CHECK-WORKSPACE",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory, Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  340,
    "dirCount":  121,
    "bytes":  90057864,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\PAGES TSX",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  214,
    "dirCount":  2,
    "bytes":  32063701,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\RECOVERED CHATS",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  5,
    "dirCount":  0,
    "bytes":  2011220330,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\Recovered_Snips",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  0,
    "dirCount":  0,
    "bytes":  null,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\repo_backup_20250816-2207",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  9634,
    "dirCount":  1794,
    "bytes":  5225602892,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\repo_backup_20250817-1445",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  20255,
    "dirCount":  3484,
    "bytes":  7285362255,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\SalvageControlSystem",
    "exists":  true,
    "type":  "directory",
    "attributes":  "Directory",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  105099,
    "dirCount":  15844,
    "bytes":  100837413680,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\071625_Grok_Cli_end of day.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  243287,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\bfg-1.14.0.jar",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  14483456,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\bfg-1.15.0.jar",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  14721936,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\CBCS-Database-MEINDL-09-16-2021-NUMBERED.accdb",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1474560,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\CBCS-Database-MEINDL-09-16-2021-NUMBERED.accde",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1413120,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax V74 Phase 6.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  23234,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax_Consultant_Standup_Templates.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  37449,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax_Implementation_Tracker.csv",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1601,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\ComplianceMax_UI_Acceptance_Criteria.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  37328,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\copy_hot_files.py",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1137,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\copy_migration_files.bat",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  968,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\DEBRIS MANAGEMENT MAP LEGEND.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  28746,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\Developing Public Assistance Hazard Mitigation Proposals.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  59417,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\enhanced-state-mitigation-planning-basics-new-enhanced-states.htm",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  119502,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\extract_final_components.py",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1362,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\final_review docs list.xlsx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  66366,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\FOIA.pdf",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  316711,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\HOT OR NOT-HOT REVIEW.xlsx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  5368806,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\HOT OR NOT.xlsx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  5368804,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\hot_review_components.csv",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  246543427,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\How to Perform a Full BCA.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  47471,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\MEINDL EEO COMPLAINT SUMMARY-05-06-25.pdf",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  197586,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\MEINDL RESUME-07-02-2024.pdf",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  214335,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\MEINDL-CASE-HS-FEMA-02430-2024-Comprehensive table of violations.pdf",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  305232,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\merge-repos.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  880,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\Modules_8_to_10_Forms_Dashboards_AI.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  37933,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\passwords.csv",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  57152,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\phase1_ahmed_task_tracker.md",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  2375,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\Process of Public Assistance Grants.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  72932,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\Public Assistance Hazard Mitigation.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  28788,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\public-assistance-cost-estimating-tool_spreadsheet_12-5-2012.xlsx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  296833,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\Rebuttal to FEMA FOIA Response 2025-FEFO-01396.markdown",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  6116,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\recovered_inventory.csv",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  10920481,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\REPOS.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  21138,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\scan_hot_review_components.py",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  3169,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\scan_recovery_for_fema_and_code.py",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  3095,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\setup-compliancemax.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1198,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\split_by_physical_size.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1108,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\split_enforced_max_75mb.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1255,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\split_final_50mb_max.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1336,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\split_guarded_absolute_50mb.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1440,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\split_real_safe_75mb.ps1",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  1193,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\This version of the code is generally well.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  18760,
    "errors":  [

               ]
}
{
    "path":  "C:\\Users\\<USER>\\Documents\\To add AI analysis to your compliance review.docx",
    "exists":  true,
    "type":  "file",
    "attributes":  "Archive",
    "isReparse":  false,
    "linkType":  null,
    "target":  [

               ],
    "accessible":  true,
    "fileCount":  1,
    "dirCount":  0,
    "bytes":  18748,
    "errors":  [

               ]
}
