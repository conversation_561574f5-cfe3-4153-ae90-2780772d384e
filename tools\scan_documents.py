
import os
import json
import time

from datetime import datetime

ROOT = r"C:\Users\<USER>\Documents"
EXCLUDE = os.path.join(ROOT, "repo analysis 202508")
OUTFILE = os.path.join(EXCLUDE, "salvage_operations", "inventory", "documents_filelist.jsonl")

def is_in_excluded(path):
    return os.path.commonpath([path, EXCLUDE]) == EXCLUDE

def get_file_metadata(path):
    try:
        size = os.path.getsize(path)
        return {
            "filename": os.path.basename(path),
            "full_path": path,
            "size_bytes": size,
            "notes": "",
            "scan_time": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return None

def main():
    total = 0
    written = 0
    with open(OUTFILE, "w", encoding="utf-8") as f:
        for root, dirs, files in os.walk(ROOT):
            if is_in_excluded(root):
                continue
            for name in files:
                path = os.path.join(root, name)
                data = get_file_metadata(path)
                if data:
                    f.write(json.dumps(data) + "\n")
                    written += 1
                total += 1
                if total % 1000 == 0:
                    print(f"Scanned {total} files... ({written} written)")
    print(f"Scan complete. {written} files written to {OUTFILE}")

if __name__ == "__main__":
    main()
