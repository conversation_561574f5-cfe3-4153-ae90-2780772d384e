@echo off
setlocal ENABLEEXTENSIONS

REM === run_salvage_scan.cmd ===
REM Run scanner (manual or agent-injected), then verify + log

set DEST=C:\Users\<USER>\Documents\repo analysis 202508
set TOOLS=%DEST%\tools
set VERIFY_JSON=%DEST%\salvage_operations\inventory\top_level_verification.jsonl
set VERIFIER=%TOOLS%\README_checker.ps1
set LOGGER=%TOOLS%\README_checker_logger.ps1

echo.
echo === [1/3] WAITING FOR SCAN COMPLETION...
echo Scanning should populate:
echo %VERIFY_JSON%
echo.
echo If the scan is already complete, press a key to continue...
pause

IF NOT EXIST "%VERIFY_JSON%" (
    echo [!] ERROR: Expected verification file not found.
    echo Make sure the scan completed and output was saved.
    pause
    exit /b 1
)

echo.
echo === [2/3] RUNNING VERIFICATION CHECK...
powershell -NoProfile -ExecutionPolicy Bypass -File "%VERIFIER%" -ReportPath "%VERIFY_JSON%"

IF %ERRORLEVEL% NEQ 0 (
    echo [!] Verification check failed or produced errors.
    pause
    exit /b 1
)

echo.
echo === [3/3] LOGGING RESULTS TO CSV...
powershell -NoProfile -ExecutionPolicy Bypass -File "%LOGGER%" -ReportPath "%VERIFY_JSON%"

echo.
echo [✓] Salvage scan validated and logged successfully.
pause
exit /b 0
