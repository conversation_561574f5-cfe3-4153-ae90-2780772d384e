---
type: "always_apply"
---

# Phase 1: Inventory Protocol (CLI Mode)

## What to Scan

- Scan the folder:  
  C:\Users\<USER>\Documents\

- Exclude the folder:  
  C:\Users\<USER>\Documents\repo analysis 202508

## What to Output

- Save results to:
  salvage_operations\inventory\documents_filelist.jsonl

- Run the scan with:
  python tools\scan_documents.py

## Post-Scan Verification

- Run the validator:  
  powershell -NoProfile -ExecutionPolicy Bypass -File tools\README_checker.ps1

- Run the scan with:
  python tools\scan_documents.py

- If validator passes, run the logger:  
  powershell -NoProfile -ExecutionPolicy Bypass -File tools\README_checker_logger.ps1

## Rules

- Do not claim scan is complete unless JSONL file exists and passes validation
- Do not skip logger unless validator fails
- Do not exclude node_modules, .git, or build directories unless explicitly instructed
- Always paste validator output — no summaries allowed

## Required Mode

- CLI Mode only
- Do not return code blocks unless asked
- Do not simulate results
