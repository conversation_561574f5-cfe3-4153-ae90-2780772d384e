$Path = "C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\documents_filelist.jsonl"

if (-Not (Test-Path $Path)) {
    Write-Host "ERROR: Expected file not found: $Path"
    exit 1
}

$lines = Get-Content -Path $Path -ErrorAction SilentlyContinue
$valid = $true

foreach ($line in $lines) {
    if (-Not ($line -match '"filename"')) {
        Write-Host "WARNING: A line is missing the filename key."
        $valid = $false
    }
}

if ($valid) {
    Write-Host "All entries look valid. Protocol followed."
    exit 0
} else {
    Write-Host "Some entries are missing required keys."
    exit 2
}
