# ComplianceMax Salvage Verification Protocol

**Purpose:** This file defines the mandatory protocol for verifying and scanning every file under `C:\Users\<USER>\Documents` (excluding `repo analysis 202508`) during salvage operations.

---

## 🔒 MANDATORY VERIFICATION RULES

Before scanning, agents must:

1. **Verify each path exists** using `VerifyPath.ps1`
2. **Capture and log:**
   - `exists`, `type`, `isReparse`, `linkType`, `target`
   - `accessible`, `fileCount`, `dirCount`, `bytes`
3. **Store results as JSONL** in:
   ```
   C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\top_level_verification.jsonl
   ```

4. **Check that reported size matches Windows file size**
   - Must be within **±2%**
   - Use:
     ```powershell
     Get-ChildItem -Recurse -File | Measure-Object Length -Sum
     ```

5. **DO NOT exclude** `.git`, `node_modules`, `build\`, or `.cache`
   - **Only exclusion**: `C:\Users\<USER>\Documents\repo analysis 202508`

---

## ⚠️ FAILURE PATTERNS TO AVOID

- Do **not** say a scanner is complete unless the **DB entry count matches file count**
- Do **not** exclude large folders based on path pattern
- Do **not** delete `.sqlite` files unless Beth explicitly says so
- Do **not** assume success based on file count alone — check byte totals
- Do **not** run a new `--wait=true` process if one is active

---

## ✅ SYSTEM COMMAND CHECKLIST

### Step 1 — Inventory All Top-Level Paths

```powershell
$Root = 'C:\Users\<USER>\Documents'
$Dest = 'C:\Users\<USER>\Documents\repo analysis 202508'
$Top = Get-ChildItem -LiteralPath $Root | Where-Object { $_.FullName -ne $Dest }
```

### Step 2 — Verify Each Path

```powershell
$Verify = Join-Path $Dest 'tools\VerifyPath.ps1'
foreach ($it in $Top) {
 & powershell -NoProfile -ExecutionPolicy Bypass -File $Verify -Path $it.FullName |
 Add-Content -Path "$Dest\salvage_operations\inventory\top_level_verification.jsonl"
}
```

### Step 3 — Sanity Check

```powershell
(Get-Content $TopReport | Select-String '^\s*"path"').Count
```

---

## 🧠 REMEMBER

This is **salvage**, not development.  
- If it's there, we scan it.  
- If it's useful, we keep it.  
- If it's big, we double check it.  
- If you're unsure — **ask Beth**.

---

## 📍End Goal

Inventory **every single file** under:

```
C:\Users\<USER>\Documents
```

**Except:**

```
C:\Users\<USER>\Documents\repo analysis 202508
```

Then move useful files into that destination after review.
