Element Name,Type,Repo,Location / File,Description,
FEMA 7-Step PA Compliance Wizard,Wizard Flow / Schema,000-RECOVERED-COMPLIANCEMAX-FILES,src/wizard/flows/fema_pa_v7.json,"Authoritative 7-step compliance intake wizard based on FEMA PA policy, covering eligibility, damage, EHP, insurance, procurement, and compliance summary.",
GROK Schema Matching Engine,AI Integration / Rule Checker,000-RECOVERED-COMPLIANCEMAX-FILES,"docs/grok_anchor_map.json, backend/services/grok_checker.py",AI-assisted rule-checking engine that maps checklist items to CFR/PAPPG citations and infers compliance via schema-to-regulation matching.,
Next.js 14 + Tailwind Frontend Architecture,Frontend Architecture,00-COMPLIANCEMAX-06212025,"src/app/, tailwind.config.ts, components/ui/","Production-grade UI built with React 18, Tailwind CSS, and Radix. Uses App Router, SSR, reusable components, and accessibility best practices.",
Glassmorphism + Framer Motion Wizard UI,Wizard UI,00-COMPLIANCEMAX-06212025,"components/wizard/StepWrapper.tsx, styles/glass.css","Animated, modern multi-step wizard interface using Framer Motion and a custom design system with responsive layouts and real-time validation.",
FastAPI Modular Backend,Backend API Service,00-COMPLIANCEMAX-06212025,"backend/api/, backend/services/, main.py","Scalable Python backend using FastAPI. Includes modules for compliance, documents, notifications, health checks, and async processing.",
Dynamic Rule Evaluation Engine,Rules Engine,codex_review,"services/rule_engine.py, data/rules/","IF-THEN-ELSE based compliance engine supporting conditional logic, trigger-based actions, policy-linked rules, and JSON-based rule configuration.",
AI Document Analyzer,AI Integration / NLP Analysis,codex_review,"services/ai_analysis.py, components/DocumentChecker.tsx","Document compliance module using GPT-style LLMs to assess uploaded content, return verdicts, suggest edits, and cite regulations (e.g., 2 CFR 200.318).",
Final Compliance Checklist (Authoritative),Structured Data,000-RECOVERED-COMPLIANCEMAX-FILES,data/fema_checklist/final_checklist.json,"Master list of compliance items and FEMA rules, tagged by PAPPG version, category (A-G), action type, and policy citation. Used by rule engine and wizard flows.",
Real-Time WebSocket Notification System,Infrastructure / Realtime Events,00-COMPLIANCEMAX-06212025,"backend/ws/events.py, frontend/hooks/useNotifications.ts","WebSocket-based realtime communication for live compliance updates, document analysis completion, and workflow transitions.",
FEMA Disaster Declarations Fetcher,External Integration,000-RECOVERED-COMPLIANCEMAX-FILES,backend/services/fema_api.py,"Cached client for OpenFEMA API v2, with real-time disaster data enrichment, used for validating eligibility and populating dropdowns in intake forms.",
