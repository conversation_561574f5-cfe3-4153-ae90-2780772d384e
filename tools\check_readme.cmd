@echo off
setlocal

REM === check_readme.cmd ===
REM Safely run the README_checker.ps1 with no PowerShell policy failures

set SCRIPT="C:\Users\<USER>\Documents\repo analysis 202508\tools\README_checker.ps1"
set REPORT="C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\top_level_verification.jsonl"

echo Running README verification...
powershell -NoProfile -ExecutionPolicy Bypass -File %SCRIPT% -ReportPath %REPORT%

IF %ERRORLEVEL% NEQ 0 (
    echo.
    echo [!] README verification failed. Review the output above.
    pause
    exit /b 1
)

echo.
echo [✓] Verification complete. No errors reported.
pause
exit /b 0
