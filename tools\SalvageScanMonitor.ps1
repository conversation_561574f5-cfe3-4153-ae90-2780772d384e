$ReportPath = "C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\top_level_verification.jsonl"
$DurationMinutes = 120
$IntervalSeconds = 120

Write-Host "=== Salvage Scan Monitor ==="
Write-Host "Monitoring: $ReportPath"
Write-Host "Duration: $DurationMinutes minutes"
Write-Host "Interval: every $IntervalSeconds seconds"
Write-Host ""

$startTime = Get-Date
$endTime = $startTime.AddMinutes($DurationMinutes)

while ((Get-Date) -lt $endTime) {
    if (Test-Path $ReportPath) {
        $lines = @(Get-Content -Path $ReportPath -ErrorAction SilentlyContinue)
        $entryCount = ($lines | Select-String '"path"').Count
        $file = Get-Item -Path $ReportPath -ErrorAction SilentlyContinue
        $timestamp = Get-Date -Format "HH:mm:ss"
        Write-Host "[$timestamp] Entries: $entryCount | Size: $($file.Length / 1MB -as [int]) MB | LastWrite: $($file.LastWriteTime)"
    } else {
        $timestamp = Get-Date -Format "HH:mm:ss"
        Write-Host "[$timestamp] File not found. Waiting..."
    }
    Start-Sleep -Seconds $IntervalSeconds
}

Write-Host "`n[MONITOR COMPLETE]"
