﻿Timestamp,Path,Exists,Accessible,Type,FileCount,Bytes,Error
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\.pytest_cache",True,True,directory,4,542,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\.venv",True,True,directory,10207,189778897,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\.vs",True,True,directory,50,1459235014,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\.vscode",True,True,directory,1,1306,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\000_another_try_at_excellence",True,True,directory,54645,653631116,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\000-NEW PROJECT-GROK CODE",True,True,directory,16,8396527,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\000-RECOVERED COMPLIANCEMAX FILES",True,True,directory,105558,19338355629,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\20250802 - Repo Notes",True,True,directory,23,22302927,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ALL CHAT DISCUSSIONS",True,True,directory,42,4326610,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ALL NEW APP",True,True,directory,228007,5962798722,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ALL NEW APP DESCRIPTIVES",True,True,directory,2,43628,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ALL-NEW-APP-CLEANED",True,True,directory,26,37784,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\augment-projects",True,True,directory,0,,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\CBCD Application V2",True,True,directory,11376,223281850,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\cbcs web site",True,True,directory,2,372271742,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\codex_review",True,True,directory,6827,2503284969,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\COMPLAINCEMAX_CLEAN",True,True,directory,187000,13669643714,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\compliancemax_clean",True,True,directory,8448,144045466,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax_Cursor_Recovery_Starter",True,True,directory,84339,4324967178,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax_Cursor_Recovery_Starter2",True,True,directory,84395,2332150748,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\compliancemax_final",True,True,directory,25,313715,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax_Rebuild_2025",True,True,directory,70,799675,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\compliancemax-fresh",True,True,directory,23264,478720008,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\compliancemax-frontend",True,True,directory,19145,405738435,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax20250403",True,True,directory,91198,2036007342,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceScan",True,True,directory,2409,37540990,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\CursorChatRecovery",True,True,directory,24,371625830,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\EaseUS_Recovery",True,True,directory,5932,15429619513,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_binary_salvage",True,True,directory,0,,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_final_recovered",True,True,directory,0,,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_large_files",True,True,directory,3,1086043821,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_large_files_extracted",True,True,directory,7761,0,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_recovery",True,True,directory,21,1086070114,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_recovery_extracted",True,True,directory,0,,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_retry",True,True,directory,21,1086070114,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\git_stash_retry_extracted",True,True,directory,0,,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\GitHub workflow results",True,True,directory,5,1832134,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\grok-cli",True,True,directory,47,57239,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\IISExpress",True,True,directory,3,84956,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\maxpmp",True,True,directory,10603,630968770,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\MAXPMP-ACCESS",True,True,directory,45,567927,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\monorepo-compliancemax-analysis",True,True,directory,1264,3087938593,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\monorepo-compliancemax-analysis-broken",True,True,directory,9072,8700730514,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\monorepo-compliancemax-analysis-cleaned",True,True,directory,69208,10859416777,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\monorepo-compliancemax-analysis.bfg",True,True,directory,30,972137,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\MORE ZIP-05-20-25",True,True,directory,252,19230237214,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\My Pictures",True,False,directory,0,0,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\PAGES TSX",True,True,directory,214,32063701,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\RECOVERED CHATS",True,True,directory,5,2011220330,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\Recovered_Snips",True,True,directory,0,,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\repo_backup_20250816-2207",True,True,directory,9634,5225602892,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\repo_backup_20250817-1445",True,True,directory,20255,7285362255,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\SalvageControlSystem",True,True,directory,105099,100837413680,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\071625_Grok_Cli_end of day.docx",True,True,file,1,243287,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\bfg-1.14.0.jar",True,True,file,1,14483456,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\bfg-1.15.0.jar",True,True,file,1,14721936,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\CBCS-Database-MEINDL-09-16-2021-NUMBERED.accdb",True,True,file,1,1474560,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\CBCS-Database-MEINDL-09-16-2021-NUMBERED.accde",True,True,file,1,1413120,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax V74 Phase 6.docx",True,True,file,1,23234,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax_Consultant_Standup_Templates.docx",True,True,file,1,37449,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax_Implementation_Tracker.csv",True,True,file,1,1601,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\ComplianceMax_UI_Acceptance_Criteria.docx",True,True,file,1,37328,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\copy_hot_files.py",True,True,file,1,1137,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\copy_migration_files.bat",True,True,file,1,968,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\DEBRIS MANAGEMENT MAP LEGEND.docx",True,True,file,1,28746,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\Developing Public Assistance Hazard Mitigation Proposals.docx",True,True,file,1,59417,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\enhanced-state-mitigation-planning-basics-new-enhanced-states.htm",True,True,file,1,119502,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\extract_final_components.py",True,True,file,1,1362,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\final_review docs list.xlsx",True,True,file,1,66366,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\FOIA.pdf",True,True,file,1,316711,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\HOT OR NOT-HOT REVIEW.xlsx",True,True,file,1,5368806,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\HOT OR NOT.xlsx",True,True,file,1,5368804,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\hot_review_components.csv",True,True,file,1,246543427,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\How to Perform a Full BCA.docx",True,True,file,1,47471,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\MEINDL EEO COMPLAINT SUMMARY-05-06-25.pdf",True,True,file,1,197586,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\MEINDL RESUME-07-02-2024.pdf",True,True,file,1,214335,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\MEINDL-CASE-HS-FEMA-02430-2024-Comprehensive table of violations.pdf",True,True,file,1,305232,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\merge-repos.ps1",True,True,file,1,880,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\Modules_8_to_10_Forms_Dashboards_AI.docx",True,True,file,1,37933,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\passwords.csv",True,True,file,1,57152,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\phase1_ahmed_task_tracker.md",True,True,file,1,2375,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\Process of Public Assistance Grants.docx",True,True,file,1,72932,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\Public Assistance Hazard Mitigation.docx",True,True,file,1,28788,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\public-assistance-cost-estimating-tool_spreadsheet_12-5-2012.xlsx",True,True,file,1,296833,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\Rebuttal to FEMA FOIA Response 2025-FEFO-01396.markdown",True,True,file,1,6116,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\recovered_inventory.csv",True,True,file,1,10920481,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\REPOS.docx",True,True,file,1,21138,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\scan_hot_review_components.py",True,True,file,1,3169,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\scan_recovery_for_fema_and_code.py",True,True,file,1,3095,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\setup-compliancemax.ps1",True,True,file,1,1198,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\split_by_physical_size.ps1",True,True,file,1,1108,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\split_enforced_max_75mb.ps1",True,True,file,1,1255,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\split_final_50mb_max.ps1",True,True,file,1,1336,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\split_guarded_absolute_50mb.ps1",True,True,file,1,1440,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\split_real_safe_75mb.ps1",True,True,file,1,1193,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\This version of the code is generally well.docx",True,True,file,1,18760,System.Collections.ArrayList
2025-08-21 11:38:23,"C:\Users\<USER>\Documents\To add AI analysis to your compliance review.docx",True,True,file,1,18748,System.Collections.ArrayList
