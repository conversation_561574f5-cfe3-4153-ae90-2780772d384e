param(
    [string]$ReportPath = "C:\Users\<USER>\Documents\repo analysis 202508\salvage_operations\inventory\top_level_verification.jsonl"
)

function Read-MultilineJsonL {
    param([string]$File)
    $items = @()
    $sb = New-Object System.Text.StringBuilder
    $depth = 0
    Get-Content -LiteralPath $File | ForEach-Object {
        $line = $_
        $depth += ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
        $depth -= ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
        [void]$sb.AppendLine($line)
        if ($depth -eq 0 -and $sb.Length -gt 0) {
            $json = $sb.ToString()
            if ($json.Trim()) { $items += ($json | ConvertFrom-Json) }
            $sb.Clear() | Out-Null
        }
    }
    return $items
}

if (-Not (Test-Path -LiteralPath $ReportPath)) {
    Write-Error "File not found: $ReportPath"
    exit 1
}

Write-Host "`n🔍 Validating:" $ReportPath
$items = Read-MultilineJsonL -File $ReportPath

$total = $items.Count
$invalid = 0
$issues = @()

foreach ($item in $items) {
    if (-not $item.exists -or -not $item.accessible) {
        $invalid++
        $issues += @{
            path = $item.path
            accessible = $item.accessible
            exists = $item.exists
            error = "Not accessible or missing"
        }
    }
    elseif ($item.bytes -lt 1 -and $item.fileCount -gt 0) {
        $invalid++
        $issues += @{
            path = $item.path
            fileCount = $item.fileCount
            bytes = $item.bytes
            error = "Zero bytes reported"
        }
    }
}

Write-Host "`n✅ Total entries:" $total
Write-Host "❌ Problem entries:" $invalid

if ($invalid -gt 0) {
    Write-Host "`n--- Issues Detected ---`n"
    $issues | Format-Table -AutoSize
} else {
    Write-Host "Validation passed"
}
